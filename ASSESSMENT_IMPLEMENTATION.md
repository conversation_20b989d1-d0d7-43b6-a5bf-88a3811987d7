# Assessment Implementation Documentation

## Overview

This document describes the implementation of the Assessment components to work with the `/api/assessment/submit` API endpoint. The implementation creates a complete assessment flow that collects data from three assessments and submits them in the required API format.

## Architecture

### Components Structure

```
src/components/Assessment/
├── AssessmentFlow.jsx          # Main orchestrator component (NEW)
├── AssessmentForm.jsx          # Individual assessment form (UPDATED)
├── AssessmentQuestion.jsx      # Question component (EXISTING)
├── AssessmentSidebar.jsx       # Progress sidebar (EXISTING)
└── AssessmentStatus.jsx        # Post-submission status (EXISTING)

src/utils/
├── assessmentTransformers.js   # Score transformation utilities (NEW)
└── testTransformers.js         # Manual testing utilities (NEW)
```

### Assessment Flow

1. **VIA Character Strengths** (Step 1) - 96 questions across 6 categories
2. **RIASEC Holland Codes** (Step 2) - 60 questions across 6 dimensions  
3. **Big Five Personality** (Step 3) - 44 questions across 5 dimensions
4. **API Submission** - Automatic submission after all assessments complete
5. **Status Tracking** - Real-time processing status with WebSocket notifications

## Key Features

### 1. AssessmentFlow Component

**Location**: `src/components/Assessment/AssessmentFlow.jsx`

**Responsibilities**:
- Orchestrates the three-step assessment process
- Manages assessment scores state
- Handles automatic progression between assessments
- Transforms scores to API format
- Submits complete assessment data to API
- Handles errors and loading states

**Key Methods**:
- `handleAssessmentSubmit()` - Stores scores and triggers next step
- `transformScoresToApiFormat()` - Converts internal scores to API format
- `submitToApi()` - Submits to `/api/assessment/submit` endpoint

### 2. Score Transformation System

**Location**: `src/utils/assessmentTransformers.js`

**Features**:
- **VIA Transformation**: Maps 6 categories to 24 character strengths with weighted distribution
- **RIASEC Transformation**: Direct mapping (categories match API fields)
- **OCEAN Transformation**: Direct mapping from Big Five to OCEAN format
- **Validation**: Comprehensive validation of all scores (0-100 range)
- **Error Handling**: Detailed error messages for invalid data

**VIA Character Strengths Mapping**:
```javascript
wisdomAndKnowledge → creativity, curiosity, judgment, loveOfLearning, perspective
courage → bravery, perseverance, honesty, zest
humanity → love, kindness, socialIntelligence
justice → teamwork, fairness, leadership
temperance → forgiveness, humility, prudence, selfRegulation
transcendence → appreciationOfBeauty, gratitude, hope, humor, spirituality
```

### 3. API Integration

**Endpoint**: `POST /api/assessment/submit`

**Request Format**:
```json
{
  "assessmentName": "AI-Driven Talent Mapping",
  "riasec": {
    "realistic": 75,
    "investigative": 80,
    "artistic": 65,
    "social": 70,
    "enterprising": 85,
    "conventional": 60
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 75,
    "extraversion": 70,
    "agreeableness": 85,
    "neuroticism": 40
  },
  "viaIs": {
    "creativity": 80,
    "curiosity": 85,
    // ... all 24 character strengths
  }
}
```

**Response Handling**:
- Success: Navigate to `/assessment/status/{jobId}`
- Error: Display error message with retry option
- Loading: Show loading spinner during submission

## Implementation Details

### Score Calculation

Each assessment uses a 1-7 Likert scale converted to 0-100:
```javascript
score = Math.round(((totalScore / questionCount) - 1) * (100 / 6))
```

### VIA Character Strengths Algorithm

1. **Category Mapping**: Each VIA category maps to specific character strengths
2. **Weighted Distribution**: Each strength has a weight within its category
3. **Variation**: Random variation added to avoid identical scores
4. **Range Validation**: All scores clamped to 0-100 range

### Error Handling

- **Validation Errors**: Detailed field-level validation messages
- **API Errors**: User-friendly error messages with retry options
- **Network Errors**: Graceful handling with retry functionality
- **Missing Data**: Default values and error prevention

### State Management

```javascript
const [assessmentScores, setAssessmentScores] = useState({
  via: null,      // VIA category scores
  riasec: null,   // RIASEC dimension scores  
  bigFive: null   // Big Five trait scores
});
```

## Testing

### Manual Testing

1. **Browser Console Test**:
   ```javascript
   // Available in browser console
   window.testAssessmentTransformers()
   ```

2. **Component Testing**:
   - Navigate to `/assessment`
   - Complete all three assessments
   - Verify automatic submission
   - Check status page navigation

### Test Data

Sample scores for testing:
```javascript
const testScores = {
  via: { wisdomAndKnowledge: 80, courage: 70, humanity: 85, justice: 75, temperance: 65, transcendence: 60 },
  riasec: { realistic: 75, investigative: 80, artistic: 65, social: 70, enterprising: 85, conventional: 60 },
  bigFive: { openness: 80, conscientiousness: 75, extraversion: 70, agreeableness: 85, neuroticism: 40 }
};
```

## Configuration

### Debug Mode

Enable debug logging in development:
```javascript
const [isDebugMode] = useState(import.meta.env.DEV && false); // Set to true for debug
```

### API Endpoints

Configured in `src/services/apiService.js`:
```javascript
ASSESSMENT: {
  SUBMIT: '/api/assessment/submit',
  STATUS: '/api/assessment/status'
}
```

## Error Scenarios

1. **Incomplete Assessments**: Error if any assessment missing
2. **Invalid Scores**: Validation catches out-of-range values
3. **API Failures**: Retry mechanism with user feedback
4. **Network Issues**: Graceful degradation with error messages

## Future Enhancements

1. **Question-Level Mapping**: Map individual VIA questions to specific strengths
2. **Adaptive Scoring**: Dynamic score calculation based on response patterns
3. **Progress Persistence**: Save progress to localStorage
4. **Offline Support**: Cache responses for offline completion
5. **Advanced Analytics**: Detailed scoring insights and explanations

## Usage

1. **Start Assessment**: Navigate to `/assessment`
2. **Complete Flow**: Answer all questions in three assessments
3. **Automatic Submission**: System submits after final assessment
4. **Track Status**: Monitor processing on status page
5. **View Results**: Access results when processing complete

The implementation provides a seamless, user-friendly assessment experience with robust error handling and comprehensive API integration.
